'use client';

import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";

const hotDeals = [
  { id: "NP15790-13", name: "LH MAIN WINDSHIELD", price: "$2,450" },
  { id: "69003810-101", name: "TOP ASSEMBLY", price: "$1,850" },
  { id: "20301573-102", name: "AIR SEPARATOR", price: "$3,200" },
  { id: "93058117", name: "ANTENNA", price: "$750" },
  { id: "066-500", name: "HYDRAULIC FILTER", price: "$425" },
];

export default function HotDeals() {
  const [position, setPosition] = useState(0);

  const scrollLeft = () => {
    setPosition((prev) => (prev <= 0 ? hotDeals.length - 1 : prev - 1));
  };

  const scrollRight = () => {
    setPosition((prev) => (prev >= hotDeals.length - 1 ? 0 : prev + 1));
  };

  // Auto-slide every 6s
  useEffect(() => {
    const interval = setInterval(scrollRight, 6000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="bg-gray-100 text-gray-800 py-3 border-t border-gray-300">
      <div className="container mx-auto px-4">
        <div className="flex items-center gap-4">
          
          {/* Label */}
          <span className="font-bold text-sm lg:text-base tracking-wide text-secondary">
            HOT DEALS:
          </span>

          {/* Prev Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={scrollLeft}
            className="text-gray-600 hover:bg-secondary hover:text-white transition-colors"
            aria-label="Previous deal"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* Deals Slider */}
          <div className="flex-1 overflow-hidden">
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${position * 300}px)` }}
            >
              {hotDeals.map((deal) => (
                <div
                  key={deal.id}
                  className="flex-shrink-0 flex items-center gap-3 pr-8"
                  style={{ minWidth: "300px" }}
                >
                  <span className="font-mono text-sm text-gray-700">{deal.id}</span>
                  <span className="text-sm text-gray-900">{deal.name}</span>
                  <span className="font-bold text-teal-700">{deal.price}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Next Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={scrollRight}
            className="text-gray-600 hover:bg-secondary hover:text-white transition-colors"
            aria-label="Next deal"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}
