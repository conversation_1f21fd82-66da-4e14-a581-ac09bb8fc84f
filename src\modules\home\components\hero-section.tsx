'use client'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import React from 'react'

const HeroSection = () => {
  return (
    <section
      className="relative h-[500px] flex items-center bg-cover bg-center"
      style={{
        backgroundImage: `url("/images/hero.jpg")`,
      }}
    >
      {/* Overlay with gradient for stylish look */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/20 to-black/10"></div>

      {/* Content */}
      <div className="relative w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-xl">
          <h2 className="text-white text-4xl sm:text-5xl font-extrabold leading-tight mb-4 drop-shadow-md">
            INSTANT <br /> PART SEARCH
          </h2>

          <p className="text-gray-200 mb-6 text-sm sm:text-base max-w-md">
            Find aircraft and helicopter parts quickly in your search.
          </p>

          {/* Search Bar */}
          <div className="flex w-full">
            <Input
              placeholder="Search by part number..."
              className="flex-1 rounded-l-lg border-white focus:ring-2 focus:ring-secondary text-white text-sm"
            />
            <Button
              className="bg-primary hover:bg-secondary rounded-none rounded-r-lg px-4"
              aria-label="Search"
            >
              <Search className="w-5 h-5 text-white" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
