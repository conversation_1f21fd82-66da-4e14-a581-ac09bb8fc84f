'use client';

import Image from 'next/image';

const logos = [
  {
    src: '/images/logo.png',
    alt: 'Royal Aeronautical Society Certification',
    width: 100,
    height: 100
  },
  {
    src: '/images/logo.png',
    alt: 'FAA Approved Supplier Certification',
    width: 100,
    height: 100
  },
  {
    src: '/images/logo.png',
    alt: 'EASA Approved Supplier Certification',
    width: 100,
    height: 100
  }
];

const ApprovedSuppliersSection = () => {
  return (
    <section className="py-8 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
          
          {/* Left Content */}
          <div className="lg:w-1/2">
            <h2 className="text-3xl font-bold text-secondary leading-tight">
              Approved Suppliers to National Carriers, Major MROs, Lessors and Government Operators
            </h2>
            <p className="mt-2 text-gray-600">
              We are trusted partners with top aviation organizations worldwide.
            </p>
          </div>

          {/* Right Content - Logos */}
          <div className="lg:w-1/2 flex items-center justify-center lg:justify-end flex-wrap gap-6">
            {logos.map((logo, index) => (
              <Image
                key={index}
                src={logo.src}
                alt={logo.alt}
                width={logo.width}
                height={logo.height}
                className="hover:scale-105 transition-transform duration-200"
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ApprovedSuppliersSection;
