'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronDown, Search, Phone, Mail, MapPin, Menu, X } from 'lucide-react';
import Image from 'next/image';

interface NavItem {
  name: string;
  href: string;
}

interface NavCategory {
  category: string;
  items: NavItem[];
}

const Navbar = () => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Aircraft dropdown menu items
  const aircraftMenuItems: NavCategory[] = [
    {
      category: 'AIRLINES',
      items: [
        { name: 'AIRBUS A330-A340', href: '/aircraft/airbus-a330-a340' },
        { name: 'AIRBUS A320 FAMILY AND NEO', href: '/aircraft/airbus-a320' },
        { name: 'BOEING B737NG', href: '/aircraft/boeing-b737ng' },
        { name: 'BOEING B727', href: '/aircraft/boeing-b727' },
        { name: 'BOEING B767/B777', href: '/aircraft/boeing-b767-b777' },
        { name: 'EMBRAER E-JET FAMILY E190 AND E170', href: '/aircraft/embraer-ejet' },
        { name: 'BAE146/AVRO RJ', href: '/aircraft/bae146-avro' },
        { name: 'BAE125-700/800 (HAWKER)', href: '/aircraft/bae125-hawker' },
        { name: 'BAE JETSTREAM 31/32', href: '/aircraft/bae-jetstream' },
        { name: 'ATR 72', href: '/aircraft/atr-72' },
        { name: 'BOMBARDIER DASH 8', href: '/aircraft/bombardier-dash8' },
        { name: 'GULFSTREAM G-IV', href: '/aircraft/gulfstream-g4' },
        { name: 'BAES HAWK JET', href: '/aircraft/baes-hawk' },
        { name: 'SAAB 340 / SAAB 2000', href: '/aircraft/saab-340-2000' }
      ],
    },
    {
      category: 'HELICOPTERS',
      items: [
        { name: 'AGUSTA AW109A, AW109C, AW109E AND AW109S', href: '/aircraft/agusta-aw109' },
        { name: 'AGUSTA AW139', href: '/aircraft/agusta-aw139' },
        { name: 'AGUSTA AW189', href: '/aircraft/agusta-aw189' },
        { name: 'AGUSTA AW101 MERLIN', href: '/aircraft/agusta-aw101' },
        { name: 'AIRBUS HELICOPTERS SUPER PUMA', href: '/aircraft/airbus-super-puma' },
        { name: 'LYNX HELICOPTERS', href: '/aircraft/lynx-helicopters' },
        { name: 'SIKORSKY S61/SEAKING', href: '/aircraft/sikorsky-s61' },
        { name: 'SIKORSKY S76 HELICOPTER SPARES', href: '/aircraft/sikorsky-s76' },
        { name: 'SIKORSKY S92', href: '/aircraft/sikorsky-s92' },
        { name: 'AIRBUS HELICOPTERS AS350', href: '/aircraft/airbus-as350' },
      ],
    },
  ];

  // About Us dropdown items
  const aboutMenuItems: NavItem[] = [
    { name: 'OUR COMPANY', href: '/about/company' },
    { name: 'OUR TEAM', href: '/about/team' },
    { name: 'OUR MISSION', href: '/about/mission' },
    { name: 'CAREERS', href: '/about/careers' },
    { name: 'NEWS & MEDIA', href: '/about/news' },
  ];

  // Tailored System Support dropdown items
  const tailoredSupportItems: NavItem[] = [
    { name: 'AIRCRAFT COMPONENT REPAIR', href: '/tailored-support/repair' },
    { name: 'ENGINEERING SERVICES', href: '/tailored-support/engineering' },
    { name: 'TECHNICAL SUPPORT', href: '/tailored-support/technical' },
    { name: 'LOGISTICS SOLUTIONS', href: '/tailored-support/logistics' },
    { name: 'INVENTORY MANAGEMENT', href: '/tailored-support/inventory' },
  ];

  // Defence dropdown items
  const defenceItems: NavItem[] = [
    { name: 'MILITARY AIRCRAFT', href: '/defence/military-aircraft' },
    { name: 'HELICOPTER SYSTEMS', href: '/defence/helicopter-systems' },
    { name: 'AVIONICS', href: '/defence/avionics' },
    { name: 'GROUND SUPPORT EQUIPMENT', href: '/defence/ground-support' },
    { name: 'TRAINING SOLUTIONS', href: '/defence/training' },
  ];

  const handleMouseEnter = (menu: string) => {
    setActiveDropdown(menu);
  };

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      console.log('Search query:', searchQuery);
      // Implement search logic here
    }
  };

  // Helper to render dropdown menu
  const renderDropdownMenu = (items: NavItem[] | NavCategory[], title: string, isCategory = false) => (
    <div
      className="absolute top-full left-0 bg-[#1E40AF] shadow-lg z-50 w-[500px] p-6 border-t-2 border-primary"
      onMouseEnter={() => setActiveDropdown(title.toLowerCase())}
      onMouseLeave={handleMouseLeave}
    >
      <h3 className="text-2xl font-bold mb-6 text-white border-l-4 border-primary pl-3">{title}</h3>
      {isCategory ? (
        <div className="grid grid-cols-2 gap-6 text-white">
          {(items as NavCategory[]).map((category) => (
            <div key={category.category}>
              <h4 className="text-lg font-semibold mb-3 flex items-center">
                <span className="w-1.5 h-6 bg-primary mr-2 inline-block rounded" />
                {category.category}
              </h4>
              <ul className="space-y-2 text-sm">
                {category.items.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="hover:text-primary cursor-pointer transition-colors block"
                      onClick={() => setActiveDropdown(null)}
                    >
                      → {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      ) : (
        <ul className="space-y-3 text-white text-sm">
          {(items as NavItem[]).map((item) => (
            <li key={item.name}>
              <Link
                href={item.href}
                className="hover:text-primary cursor-pointer transition-colors block"
                onClick={() => setActiveDropdown(null)}
              >
                → {item.name}
              </Link>
            </li>
          ))}
        </ul>
      )}
    </div>
  );

  return (
    <div className="bg-white">
      {/* Top Contact & Search Section */}
      <header className="bg-[#F9FAFB] border-b border-gray-200">
        <div className="container mx-auto px-2 md:px-4">
          <div className="flex items-center justify-between py-4">
            {/* Logo */}
            <Link href="/" className="">
              <Image
                src="/images/logo.png"
                alt="Mustang Airworks Logo"
                width={200}
                height={100}
                className="h-20 w-auto"
              />
            </Link>

            {/* Contact Info - Desktop */}
            <div className="hidden lg:flex flex-col space-y-2 text-primary">
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span className="font-medium">+977-9801000016</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4" />
                <span className="font-medium">Lazimpat, Kathmandu</span>
              </div>
            </div>

            {/* Search Form */}
            <div className="hidden md:flex justify-end flex-1 max-w-md">
              <form onSubmit={handleSearch} className="relative w-full">
                <input
                  type="text"
                  placeholder="SEARCH FOR PARTS OR PAGES"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pr-10 py-2 px-3 bg-white border border-[#1E40AF] rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <button
                  type="submit"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary hover:text-[#1E40AF]"
                  aria-label="Search"
                >
                  <Search className="w-5 h-5" />
                </button>
              </form>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden text-[#1E40AF] hover:text-primary transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Navigation with Dropdowns */}
        <nav className="bg-[#1E40AF] text-white relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-12">
              {/* Desktop Navigation */}
              <div className="hidden lg:flex space-x-8 font-medium text-sm">
                <Link
                  href="/"
                  className="hover:text-primary transition-colors"
                >
                  HOME
                </Link>

                {/* Aircraft Dropdown */}
                <div
                  className="relative"
                  onMouseEnter={() => handleMouseEnter('aircraft')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    href="/aircraft"
                    className="flex items-center hover:text-primary transition-colors"
                  >
                    AIRCRAFT <ChevronDown className="ml-1 w-4 h-4" />
                  </Link>
                  {activeDropdown === 'aircraft' && renderDropdownMenu(aircraftMenuItems, 'AIRCRAFT', true)}
                </div>

                {/* About Us Dropdown */}
                <div
                  className="relative"
                  onMouseEnter={() => handleMouseEnter('about')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    href="/about"
                    className="flex items-center hover:text-primary transition-colors"
                  >
                    ABOUT US <ChevronDown className="ml-1 w-4 h-4" />
                  </Link>
                  {activeDropdown === 'about' && renderDropdownMenu(aboutMenuItems, 'ABOUT US')}
                </div>

                {/* Tailored System Support Dropdown */}
                <div
                  className="relative"
                  onMouseEnter={() => handleMouseEnter('tailored')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    href="/tailored-support"
                    className="flex items-center hover:text-primary transition-colors"
                  >
                    TAILORED SYSTEM SUPPORT <ChevronDown className="ml-1 w-4 h-4" />
                  </Link>
                  {activeDropdown === 'tailored' && renderDropdownMenu(tailoredSupportItems, 'TAILORED SYSTEM SUPPORT')}
                </div>

                {/* Defence Dropdown */}
                <div
                  className="relative"
                  onMouseEnter={() => handleMouseEnter('defence')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    href="/defence"
                    className="flex items-center hover:text-primary transition-colors"
                  >
                    DEFENCE <ChevronDown className="ml-1 w-4 h-4" />
                  </Link>
                  {activeDropdown === 'defence' && renderDropdownMenu(defenceItems, 'DEFENCE')}
                </div>

                <Link
                  href="/certified"
                  className="hover:text-primary transition-colors"
                >
                  CERTIFIED
                </Link>

                <Link
                  href="/contact"
                  className="hover:text-primary transition-colors"
                >
                  CONTACT US
                </Link>
              </div>

              {/* LINE CARD Button */}
              <div className="hidden lg:block">
                <Link
                  href="/line-card"
                  className="bg-primary hover:bg-red-700 px-4 py-1 rounded text-xs font-semibold transition-colors"
                >
                  LINE CARD 2025
                </Link>
              </div>

              {/* Mobile Menu Toggle Text */}
              <div className="lg:hidden text-sm font-medium text-[#1E40AF]">
                {isMenuOpen ? 'Close Menu' : 'Menu'}
              </div>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden bg-[#1E40AF] border-t border-primary text-white">
              <div className="px-4 py-4 space-y-4">
                {/* Mobile Contact Info */}
                <div className="space-y-2 text-sm border-b border-primary pb-4 text-primary">
                  <div className="flex items-center space-x-2">
                    <Phone className="w-3 h-3" />
                    <span>+977-1-4169802</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-3 h-3" />
                    <span>+977-9801000016</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-3 h-3" />
                    <span><EMAIL></span>
                  </div>
                </div>

                {/* Mobile Navigation Links */}
                <Link
                  href="/"
                  className="block hover:text-primary font-bold py-2 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  HOME
                </Link>

                {/* Mobile Aircraft Menu */}
                <div>
                  <button
                    onClick={() =>
                      setActiveDropdown(activeDropdown === 'mobile-aircraft' ? null : 'mobile-aircraft')
                    }
                    className="flex items-center justify-between w-full hover:text-primary font-bold py-2 transition-colors"
                  >
                    <span>AIRCRAFT</span>
                    <ChevronDown
                      className={`w-4 h-4 transform transition-transform ${
                        activeDropdown === 'mobile-aircraft' ? 'rotate-180' : ''
                      }`}
                    />
                  </button>

                  {activeDropdown === 'mobile-aircraft' && (
                    <div className="pl-4 mt-2 space-y-3 text-primary">
                      {aircraftMenuItems.map((category) => (
                        <div key={category.category}>
                          <h4 className="font-semibold mb-2 text-sm">{category.category}</h4>
                          <div className="space-y-1">
                            {category.items.map((item) => (
                              <Link
                                key={item.name}
                                href={item.href}
                                className="block text-xs hover:text-white py-1 pl-2 transition-colors"
                                onClick={() => {
                                  setIsMenuOpen(false);
                                  setActiveDropdown(null);
                                }}
                              >
                                → {item.name}
                              </Link>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mobile About Us Menu */}
                <div>
                  <button
                    onClick={() =>
                      setActiveDropdown(activeDropdown === 'mobile-about' ? null : 'mobile-about')
                    }
                    className="flex items-center justify-between w-full hover:text-primary font-medium py-2 transition-colors"
                  >
                    <span>ABOUT US</span>
                    <ChevronDown
                      className={`w-4 h-4 transform transition-transform ${
                        activeDropdown === 'mobile-about' ? 'rotate-180' : ''
                      }`}
                    />
                  </button>

                  {activeDropdown === 'mobile-about' && (
                    <div className="pl-4 mt-2 space-y-2 text-primary">
                      {aboutMenuItems.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="block text-xs hover:text-white py-1 pl-2 transition-colors"
                          onClick={() => {
                            setIsMenuOpen(false);
                            setActiveDropdown(null);
                          }}
                        >
                          → {item.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mobile Tailored Support Menu */}
                <div>
                  <button
                    onClick={() =>
                      setActiveDropdown(activeDropdown === 'mobile-tailored' ? null : 'mobile-tailored')
                    }
                    className="flex items-center justify-between w-full hover:text-primary font-medium py-2 transition-colors"
                  >
                    <span>TAILORED SYSTEM SUPPORT</span>
                    <ChevronDown
                      className={`w-4 h-4 transform transition-transform ${
                        activeDropdown === 'mobile-tailored' ? 'rotate-180' : ''
                      }`}
                    />
                  </button>

                  {activeDropdown === 'mobile-tailored' && (
                    <div className="pl-4 mt-2 space-y-2 text-primary">
                      {tailoredSupportItems.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="block text-xs hover:text-white py-1 pl-2 transition-colors"
                          onClick={() => {
                            setIsMenuOpen(false);
                            setActiveDropdown(null);
                          }}
                        >
                          → {item.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>

                {/* Mobile Defence Menu */}
                <div>
                  <button
                    onClick={() =>
                      setActiveDropdown(activeDropdown === 'mobile-defence' ? null : 'mobile-defence')
                    }
                    className="flex items-center justify-between w-full hover:text-primary font-medium py-2 transition-colors"
                  >
                    <span>DEFENCE</span>
                    <ChevronDown
                      className={`w-4 h-4 transform transition-transform ${
                        activeDropdown === 'mobile-defence' ? 'rotate-180' : ''
                      }`}
                    />
                  </button>

                  {activeDropdown === 'mobile-defence' && (
                    <div className="pl-4 mt-2 space-y-2 text-primary">
                      {defenceItems.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="block text-xs hover:text-white py-1 pl-2 transition-colors"
                          onClick={() => {
                            setIsMenuOpen(false);
                            setActiveDropdown(null);
                          }}
                        >
                          → {item.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>

                <Link
                  href="/certified"
                  className="block hover:text-primary font-medium py-2 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  CERTIFIED
                </Link>

                <Link
                  href="/contact"
                  className="block hover:text-primary font-medium py-2 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  CONTACT US
                </Link>

                {/* Mobile LINE CARD Button */}
                <Link
                  href="/line-card"
                  className="block bg-primary hover:bg-red-700 px-4 py-2 rounded text-center text-sm font-semibold transition-colors mt-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  LINE CARD 2025
                </Link>
              </div>
            </div>
          )}
        </nav>
      </header>
    </div>
  );
};

export default Navbar;
