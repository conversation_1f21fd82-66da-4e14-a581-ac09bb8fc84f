'use client';

import Image from 'next/image';
import { useState } from 'react';

// Example Team Data
const team = [
  {
    name: "<PERSON><PERSON><PERSON>",
    title: "CFP Professional",
    photo: "/images/team/durriya-photo.jpg",
    video: "/videos/team/durriya-intro.mp4", // Video source for hover and modal
    bio: "<PERSON><PERSON><PERSON> has 6 years of financial planning experience and 4 years of tax-focused expertise. Loves making complex concepts simple, and previously worked at Merrill Lynch, E&Y, and Albert.",
    tags: ["Kind", "Strategic", "Insightful"],
  },
  // Add more members...
];

export default function OurTeamSection() {
  const [selected, setSelected] = useState(null);

  return (
    <section className="py-14 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <h2 className="text-3xl font-extrabold mb-8 text-secondary">Meet the Team</h2>
        <div className="flex gap-6 overflow-x-auto pb-4">
          {team.map((member, i) => (
            <div key={i} className="group relative w-56 flex-shrink-0">
              {/* Card with hover video */}
              <div
                className="rounded-xl bg-white shadow-md cursor-pointer overflow-hidden transition ring-2 ring-transparent group-hover:ring-primary"
                onClick={() => setSelected(member)}
              >
                {/* Video on hover, else static photo */}
                <div className="relative aspect-square w-full h-56 overflow-hidden">
                  {/* Hide static image on hover, reveal video */}
                  <Image
                    src={member.photo}
                    alt={member.name}
                    className="w-full h-full object-cover transition-opacity duration-300 group-hover:opacity-0"
                  />
                  {/* Inline muted video for hover */}
                  <video
                    src={member.video}
                    className="absolute inset-0 w-full h-full object-cover rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    muted
                    autoPlay
                    loop
                    playsInline
                  />
                </div>
                <div className="py-4 px-3 text-center">
                  <h3 className="text-lg font-semibold text-gray-900">{member.name}</h3>
                  <p className="text-xs text-gray-600">{member.title}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Modal/Dialog for full bio and main video */}
        {selected && (
          <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full relative">
              <button
                className="absolute top-3 right-3 text-gray-500 hover:text-primary text-lg"
                onClick={() => setSelected(null)}
                aria-label="Close"
              >
                ×
              </button>
              <div className="mb-5">
                {/* Large video if available, else photo */}
                {selected.video ? (
                  <video
                    src={selected.video}
                    className="w-full h-60 object-cover rounded-xl mb-4"
                    autoPlay
                    muted
                    loop
                    playsInline
                  />
                ) : (
                  <img src={selected.photo} className="w-full h-60 object-cover rounded-xl mb-4" alt={selected.name} />
                )}
              </div>
              <h3 className="text-2xl font-bold mb-2 text-secondary">{selected.name}</h3>
              <p className="text-sm text-gray-700 mb-3">{selected.title}</p>
              <p className="text-gray-600 mb-4">{selected.bio}</p>
              <div className="flex gap-2">
                {selected.tags.map(tag => (
                  <span key={tag} className="px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
