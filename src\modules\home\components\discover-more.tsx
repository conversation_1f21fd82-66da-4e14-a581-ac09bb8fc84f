'use client';

import Link from 'next/link';

const DiscoverMore = () => {
  return (
    <section className="relative h-[500px] overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url("/images/download.jpeg")`,
        }}
      />

      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/10" />

      {/* Aircraft Illustration */}
      {/* <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/4">
        <svg 
          width="600" 
          height="200" 
          viewBox="0 0 600 200" 
          className="opacity-80"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            <linearGradient id="aircraftGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style={{stopColor: '#e5e7eb', stopOpacity: 1}} />
              <stop offset="50%" style={{stopColor: '#f9fafb', stopOpacity: 1}} />
              <stop offset="100%" style={{stopColor: '#d1d5db', stopOpacity: 1}} />
            </linearGradient>
          </defs>
          
          <ellipse cx="250" cy="100" rx="180" ry="25" fill="url(#aircraftGradient)" />
          
          <ellipse cx="250" cy="100" rx="250" ry="8" fill="#d1d5db" opacity="0.8" />
          
          <path d="M50 100 L80 80 L80 120 Z" fill="url(#aircraftGradient)" />
          <path d="M50 100 L70 85 L70 100 Z" fill="#d1d5db" />
          
          <ellipse cx="200" cy="120" rx="15" ry="35" fill="#9ca3af" />
          <ellipse cx="300" cy="120" rx="15" ry="35" fill="#9ca3af" />
          
          {/* Landing Gear *
          <circle cx="180" cy="140" r="8" fill="#374151" />
          <circle cx="250" cy="140" r="8" fill="#374151" />
          <circle cx="320" cy="140" r="8" fill="#374151" />
          
          {/* Windows *
          <rect x="120" y="90" width="200" height="8" rx="4" fill="#3b82f6" opacity="0.6" />
          
          {/* Cockpit *
          <path d="M380 90 Q420 85 450 100 Q420 115 380 110 Z" fill="#1e40af" opacity="0.7" />
        </svg>
      </div> */}

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="max-w-7xl mx-auto px-4 w-full">
          <div className="max-w-2xl">
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
              FIXED-WING SUPPORT
            </h2>
            
            <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
              CUREENTLY STOCKING IN EXCESS OF ROTORY AND FIXED WING AICRAFT PARTS
            </p>
            
            <Link 
              href="/aircraft/fixed-wing"
              className="inline-block"
            >
              <button className="group relative px-8 py-3 border-2 bg-primary border-white text-white font-semibold text-lg tracking-wider hover:bg-secondary hover:text-white transition-all duration-300 transform hover:scale-105">
                <span className="relative z-10">DISCOVER MORE</span>
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-white transform scale-x-100 group-hover:scale-x-0 transition-transform duration-300" />
              </button>
            </Link>
          </div>
        </div>
      </div>


    </section>
  );
};

export default DiscoverMore;