import { Phone, Mail, MapPin } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-secondary text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="flex items-center gap-4 mb-6">
              <div>
                <h3 className="text-xl font-bold">MUSTANG AIRWORKS</h3>
                <p className="text-sm opacity-90">Pvt. Ltd.</p>
              </div>
            </div>
            <p className="text-sm opacity-90 leading-relaxed mb-6">
              Your trusted aerospace parts supplier in Nepal and South Asia. 
              We provide certified aircraft parts, lubricants, tires and technical 
              consulting services for airlines, helicopters, and defense aircraft.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-bold mb-4">Quick Links</h4>
            <nav className="space-y-2">
              <a href="#home" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">Home</a>
              <a href="#aircraft" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">Aircraft</a>
              <a href="#about" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">About Us</a>
              <a href="#support" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">System Support</a>
              <a href="#defence" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">Defence</a>
              <a href="#certified" className="block text-sm opacity-90 hover:opacity-100 transition-smooth">Certified</a>
            </nav>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="font-bold mb-4">Contact Us</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 opacity-90" />
                <span className="text-sm opacity-90">+977 (1) 234-5678</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 opacity-90" />
                <span className="text-sm opacity-90"><EMAIL></span>
              </div>
              <div className="flex items-start gap-3">
                <MapPin className="h-4 w-4 opacity-90 mt-0.5" />
                <span className="text-sm opacity-90">
                  Kathmandu, Nepal<br />
                  South Asia Operations
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-primary-foreground/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm opacity-90">
              © 2024 Mustang Airworks Pvt. Ltd. All rights reserved.
            </p>
            <div className="flex gap-6">
              <a href="#" className="text-sm opacity-90 hover:opacity-100 transition-smooth">Privacy Policy</a>
              <a href="#" className="text-sm opacity-90 hover:opacity-100 transition-smooth">Terms of Service</a>
              <a href="#" className="text-sm opacity-90 hover:opacity-100 transition-smooth">Quality Certifications</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;