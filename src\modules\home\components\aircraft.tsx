'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronRight, Plane, RotateCcw } from 'lucide-react';

const AircraftSection = () => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  const aircraftData = {
    airline: {
      title: "AIRLINE",
      icon: Plane,
      image: "/images/airline.png",
      gradient: "from-blue-900 via-blue-700 to-blue-500",
      aircraft: [
        { name: "AIRBUS A330", href: "/aircraft/airbus-a330" },
        { name: "AIRBUS A320", href: "/aircraft/airbus-a320" },
        { name: "ATR 72", href: "/aircraft/atr-72" },
        { name: "BOMBARDIER DASH Q400", href: "/aircraft/bombardier-dash-q400" },
        { name: "CRJ 200/700", href: "/aircraft/crj-200-700" }
      ]
    },
    helicopter: {
      title: "HELICOPTER",
      icon: RotateCcw,
      image: "/images/helicopter.png",
      gradient: "from-sky-600 via-blue-500 to-cyan-400",
      aircraft: [
        { name: "AIRBUS AS350(H125)", href: "/aircraft/airbus-as350" },
        { name: "AIEBUS H130", href: "/aircraft/airbus-h130" },
        { name: "BELL 407", href: "/aircraft/bell-407" },
        { name: "BELL 505", href: "/aircraft/bell-505" },
        { name: "AGUSTA WESTLAND AW-139S", href: "/aircraft/agusta-aw139s" }
      ]
    }
  };


  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-teal-500 to-blue-600"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">

        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent mb-4">
            AIRCRAFT SUPPORT
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive parts and maintenance support for airlines and helicopters across Nepal and South Asia
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-teal-500 to-blue-500 mx-auto mt-6 rounded-full" />
        </div>

        {/* Aircraft Cards Grid */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">

          {/* Airline Card */}
          <div
            className={`group relative overflow-hidden rounded-3xl shadow-2xl transition-all duration-500 transform ${hoveredCard === 'airline' ? 'scale-105 shadow-3xl' : 'hover:scale-102'
              }`}
            onMouseEnter={() => setHoveredCard('airline')}
            onMouseLeave={() => setHoveredCard(null)}
          >
            {/* Background Image */}
            <div className="relative h-80 overflow-hidden">
              {/* Actual Image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-700 group-hover:scale-110"
                style={{
                  backgroundImage: `url(${aircraftData.airline.image})`
                }}
              />

              {/* Fallback gradient if image doesn't load */}
              {/* <div className={`absolute inset-0 bg-gradient-to-br ${aircraftData.airline.gradient} opacity-80`} /> */}

              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

              {/* Content */}
              <div className="absolute inset-0 flex flex-col justify-end p-8">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mr-4">
                    <Plane className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-white tracking-wide drop-shadow-lg">
                    {aircraftData.airline.title}
                  </h3>
                </div>
              </div>
            </div>

            {/* Aircraft List */}
            <div className="bg-white p-8">
              <ul className="space-y-3">
                {aircraftData.airline.aircraft.map((aircraft, index) => (
                  <li key={index}>
                    <Link
                      href={aircraft.href}
                      className="group/item flex items-center justify-between py-3 px-4 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-teal-50 transition-all duration-300"
                    >
                      <div className="flex items-center">
                        <ChevronRight className="w-4 h-4 text-teal-500 mr-3 group-hover/item:translate-x-1 transition-transform" />
                        <span className="font-medium text-gray-700 group-hover/item:text-teal-600 transition-colors">
                          {aircraft.name}
                        </span>
                      </div>
                      <div className="w-2 h-2 bg-teal-500 rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity" />
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Helicopter Card */}
          <div
            className={`group relative overflow-hidden rounded-3xl shadow-2xl transition-all duration-500 transform ${hoveredCard === 'helicopter' ? 'scale-105 shadow-3xl' : 'hover:scale-102'
              }`}
            onMouseEnter={() => setHoveredCard('helicopter')}
            onMouseLeave={() => setHoveredCard(null)}
          >
            {/* Background Image */}
            <div className="relative h-80 overflow-hidden">
              {/* Actual Image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-700 group-hover:scale-110"
                style={{
                  backgroundImage: `url(${aircraftData.helicopter.image})`
                }}
              />

              {/* Fallback gradient if image doesn't load */}
              {/* <div className={`absolute inset-0 bg-gradient-to-br ${aircraftData.helicopter.gradient} opacity-80`} /> */}

              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

              {/* Content */}
              <div className="absolute inset-0 flex flex-col justify-end p-8">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mr-4">
                    <RotateCcw className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-white tracking-wide drop-shadow-lg">
                    {aircraftData.helicopter.title}
                  </h3>
                </div>
              </div>
            </div>

            {/* Aircraft List */}
            <div className="bg-white p-8">
              <ul className="space-y-3">
                {aircraftData.helicopter.aircraft.map((aircraft, index) => (
                  <li key={index}>
                    <Link
                      href={aircraft.href}
                      className="group/item flex items-center justify-between py-3 px-4 rounded-xl hover:bg-gradient-to-r hover:from-sky-50 hover:to-cyan-50 transition-all duration-300"
                    >
                      <div className="flex items-center">
                        <ChevronRight className="w-4 h-4 text-sky-500 mr-3 group-hover/item:translate-x-1 transition-transform" />
                        <span className="font-medium text-gray-700 group-hover/item:text-sky-600 transition-colors">
                          {aircraft.name}
                        </span>
                      </div>
                      <div className="w-2 h-2 bg-sky-500 rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity" />
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Link
            href="/aircraft"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal-600 to-blue-600 text-white font-semibold text-lg rounded-full hover:from-teal-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <span>Explore All Aircraft</span>
            <ChevronRight className="w-5 h-5 ml-2" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default AircraftSection;